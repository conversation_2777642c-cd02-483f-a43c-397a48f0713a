'use client';

import { useState } from "react";
import Link from "next/link";
import { Calendar, MapPin, Clock, Users, ArrowRight, Filter } from "lucide-react";

const categories = ["All", "Workshop", "Networking", "Showcase", "Masterclass", "Summit"];

interface Event {
  id: number;
  title: string;
  description: string;
  date: string;
  time: string;
  location: string;
  category: string;
  image: string;
  capacity: number;
  registered: number;
  price: string;
  featured: boolean;
}

function EventCard({ event }: { event: Event }) {
  const eventDate = new Date(event.date);
  const isUpcoming = eventDate > new Date();
  const spotsLeft = event.capacity - event.registered;

  return (
    <div className="bg-gray-900 rounded-lg overflow-hidden hover:transform hover:scale-105 transition-all duration-300 shadow-lg">
      <div className="relative h-48 bg-gray-800">
        {/* Placeholder for event image */}
        <div className="absolute inset-0 bg-gradient-to-br from-red-600/20 to-gray-900/80 flex items-center justify-center">
          <Calendar className="h-16 w-16 text-gray-400" />
        </div>
        {event.featured && (
          <div className="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
            Featured
          </div>
        )}
        <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
          {event.category}
        </div>
      </div>
      
      <div className="p-6">
        <h3 className="text-xl font-montserrat font-bold text-white mb-2 line-clamp-2">
          {event.title}
        </h3>
        
        <p className="text-gray-300 text-sm mb-4 line-clamp-3">
          {event.description}
        </p>
        
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-gray-400 text-sm">
            <Calendar className="h-4 w-4 mr-2" />
            {eventDate.toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </div>
          
          <div className="flex items-center text-gray-400 text-sm">
            <Clock className="h-4 w-4 mr-2" />
            {event.time}
          </div>
          
          <div className="flex items-center text-gray-400 text-sm">
            <MapPin className="h-4 w-4 mr-2" />
            {event.location}
          </div>
          
          <div className="flex items-center text-gray-400 text-sm">
            <Users className="h-4 w-4 mr-2" />
            {event.registered}/{event.capacity} registered
            {spotsLeft > 0 && spotsLeft <= 10 && (
              <span className="ml-2 text-orange-400 font-semibold">
                ({spotsLeft} spots left)
              </span>
            )}
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="text-white font-bold text-lg">
            {event.price}
          </div>
          
          <Link
            href={`/events/${event.id}`}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center space-x-2"
          >
            <span>Learn More</span>
            <ArrowRight className="h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  );
}

interface EventsClientProps {
  events: Event[];
}

export default function EventsClient({ events }: EventsClientProps) {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  
  const filteredEvents = events.filter(event => {
    const matchesCategory = selectedCategory === "All" || event.category === selectedCategory;
    const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <section id="upcoming-events" className="py-16 px-4 bg-gray-900/30">
      <div className="max-w-7xl mx-auto">
        <h2 className="text-3xl md:text-4xl font-bebas tracking-wider text-center mb-12">
          ALL UPCOMING EVENTS
        </h2>
        
        {/* Search and Filter Controls */}
        <div className="flex flex-col lg:flex-row gap-6 mb-12">
          {/* Search Bar */}
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="Search events..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-600 focus:ring-1 focus:ring-red-600"
              />
              <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            </div>
          </div>
          
          {/* Category Filter */}
          <div className="flex flex-wrap gap-3">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-3 rounded-full border transition-all duration-200 font-semibold ${
                  selectedCategory === category
                    ? 'border-red-600 bg-red-600 text-white'
                    : 'border-gray-600 text-gray-300 hover:border-red-600 hover:text-red-400'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
        
        {/* Results Count */}
        <div className="text-center mb-8">
          <p className="text-gray-400">
            Showing {filteredEvents.length} of {events.length} events
            {selectedCategory !== "All" && ` in ${selectedCategory}`}
            {searchTerm && ` matching "${searchTerm}"`}
          </p>
        </div>
        
        {/* Events Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredEvents.length > 0 ? (
            filteredEvents.map((event) => (
              <EventCard key={event.id} event={event} />
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <Calendar className="h-16 w-16 text-gray-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-400 mb-2">No events found</h3>
              <p className="text-gray-500">
                Try adjusting your search terms or category filter.
              </p>
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
