import Image from "next/image";
import Link from "next/link";
import { Calendar, MapPin, Clock, Users, ArrowRight } from "lucide-react";
import { Metadata } from "next";
import EventsClient from "./events-client";

// Static generation configuration
export const dynamic = 'force-static';
export const revalidate = false;

export const metadata: Metadata = {
  title: 'Events - Del York Creative Academy',
  description: 'Discover upcoming events, workshops, and creative gatherings at Del York Creative Academy. Join our community of creative professionals.',
  keywords: 'events, workshops, creative academy, film, media, technology, Africa',
};

// Mock events data - in a real app, this would come from a CMS or API
const events = [
  {
    id: 1,
    title: "Creative Storytelling Workshop",
    description: "Learn the fundamentals of visual storytelling from industry professionals. This hands-on workshop covers narrative structure, character development, and visual composition.",
    date: "2024-09-15",
    time: "10:00 AM - 4:00 PM",
    location: "Del York Creative Academy Campus",
    category: "Workshop",
    image: "/images/events/storytelling-workshop.jpg",
    capacity: 25,
    registered: 18,
    price: "Free",
    featured: true
  },
  {
    id: 2,
    title: "Film Industry Networking Night",
    description: "Connect with fellow filmmakers, producers, and creative professionals. An evening of networking, collaboration opportunities, and industry insights.",
    date: "2024-09-22",
    time: "6:00 PM - 9:00 PM",
    location: "Lagos Creative Hub",
    category: "Networking",
    image: "/images/events/networking-night.jpg",
    capacity: 100,
    registered: 67,
    price: "₦5,000",
    featured: false
  },
  {
    id: 3,
    title: "Digital Marketing for Creatives",
    description: "Master the art of promoting your creative work online. Learn social media strategies, content creation, and brand building specifically for creative professionals.",
    date: "2024-09-28",
    time: "2:00 PM - 6:00 PM",
    location: "Online (Zoom)",
    category: "Workshop",
    image: "/images/events/digital-marketing.jpg",
    capacity: 50,
    registered: 32,
    price: "₦10,000",
    featured: true
  },
  {
    id: 4,
    title: "African Cinema Showcase",
    description: "A celebration of contemporary African cinema featuring screenings, panel discussions with filmmakers, and Q&A sessions.",
    date: "2024-10-05",
    time: "4:00 PM - 10:00 PM",
    location: "Terra Kulture Arena",
    category: "Showcase",
    image: "/images/events/cinema-showcase.jpg",
    capacity: 200,
    registered: 145,
    price: "₦3,000",
    featured: false
  },
  {
    id: 5,
    title: "Documentary Filmmaking Masterclass",
    description: "An intensive masterclass on documentary production, from concept development to post-production, led by award-winning documentary filmmakers.",
    date: "2024-10-12",
    time: "9:00 AM - 5:00 PM",
    location: "Del York Creative Academy Campus",
    category: "Masterclass",
    image: "/images/events/documentary-masterclass.jpg",
    capacity: 30,
    registered: 22,
    price: "₦25,000",
    featured: true
  },
  {
    id: 6,
    title: "Creative Technology Summit",
    description: "Explore the intersection of creativity and technology. Sessions on VR/AR in storytelling, AI in creative processes, and emerging tech trends.",
    date: "2024-10-20",
    time: "9:00 AM - 6:00 PM",
    location: "Landmark Centre, Victoria Island",
    category: "Summit",
    image: "/images/events/tech-summit.jpg",
    capacity: 300,
    registered: 189,
    price: "₦15,000",
    featured: false
  }
];

const categories = ["All", "Workshop", "Networking", "Showcase", "Masterclass", "Summit"];

function EventCard({ event }: { event: typeof events[0] }) {
  const eventDate = new Date(event.date);
  const isUpcoming = eventDate > new Date();
  const spotsLeft = event.capacity - event.registered;

  return (
    <div className="bg-gray-900 rounded-lg overflow-hidden hover:transform hover:scale-105 transition-all duration-300 shadow-lg">
      <div className="relative h-48 bg-gray-800">
        {/* Placeholder for event image */}
        <div className="absolute inset-0 bg-gradient-to-br from-red-600/20 to-gray-900/80 flex items-center justify-center">
          <Calendar className="h-16 w-16 text-gray-400" />
        </div>
        {event.featured && (
          <div className="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
            Featured
          </div>
        )}
        <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
          {event.category}
        </div>
      </div>
      
      <div className="p-6">
        <h3 className="text-xl font-montserrat font-bold text-white mb-2 line-clamp-2">
          {event.title}
        </h3>
        
        <p className="text-gray-300 text-sm mb-4 line-clamp-3">
          {event.description}
        </p>
        
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-gray-400 text-sm">
            <Calendar className="h-4 w-4 mr-2" />
            {eventDate.toLocaleDateString('en-US', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
          </div>
          
          <div className="flex items-center text-gray-400 text-sm">
            <Clock className="h-4 w-4 mr-2" />
            {event.time}
          </div>
          
          <div className="flex items-center text-gray-400 text-sm">
            <MapPin className="h-4 w-4 mr-2" />
            {event.location}
          </div>
          
          <div className="flex items-center text-gray-400 text-sm">
            <Users className="h-4 w-4 mr-2" />
            {event.registered}/{event.capacity} registered
            {spotsLeft > 0 && spotsLeft <= 10 && (
              <span className="ml-2 text-orange-400 font-semibold">
                ({spotsLeft} spots left)
              </span>
            )}
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="text-white font-bold text-lg">
            {event.price}
          </div>
          
          <Link
            href={`/events/${event.id}`}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors flex items-center space-x-2"
          >
            <span>Learn More</span>
            <ArrowRight className="h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function EventsPage() {
  const featuredEvents = events.filter(event => event.featured);
  const upcomingEvents = events.filter(event => new Date(event.date) > new Date());

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Hero Section */}
      <section className="relative py-20 px-4 bg-gradient-to-br from-red-900/20 to-black">
        <div className="absolute inset-0 bg-[url('/images/page-header-bg.png')] bg-cover bg-center opacity-20"></div>
        <div className="relative max-w-7xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bebas tracking-wider text-white mb-6">
            EVENTS & WORKSHOPS
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Join our vibrant community of creative professionals. Discover workshops, networking events, 
            and showcases designed to elevate your creative journey and expand your professional network.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="#upcoming-events"
              className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              View Upcoming Events
            </Link>
            <Link
              href="/contact"
              className="border border-white text-white hover:bg-white hover:text-black px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Host an Event
            </Link>
          </div>
        </div>
      </section>

      {/* Featured Events */}
      {featuredEvents.length > 0 && (
        <section className="py-16 px-4">
          <div className="max-w-7xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bebas tracking-wider text-center mb-12">
              FEATURED EVENTS
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredEvents.map((event) => (
                <EventCard key={event.id} event={event} />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Interactive Events Section */}
      <EventsClient events={upcomingEvents} />

      {/* Call to Action */}
      <section className="py-20 px-4 bg-gradient-to-r from-red-900/20 to-black">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bebas tracking-wider mb-6">
            STAY UPDATED ON UPCOMING EVENTS
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Be the first to know about new workshops, networking opportunities, and exclusive events. 
            Join our community and never miss out on advancing your creative career.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/register"
              className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Join Our Community
            </Link>
            <Link
              href="/contact"
              className="border border-white text-white hover:bg-white hover:text-black px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
